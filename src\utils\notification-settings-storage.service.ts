/**
 * Notification Settings Storage Service
 *
 * Provides file-based storage for notification settings using JSON files.
 * Implements atomic operations, file locking, and proper error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import { LoggerService } from './logger.service';

export interface NotificationSettingsData {
  id: string;
  user_id: string;
  agent_assigned: boolean;
  case_status_update: boolean;
  agent_query: boolean;
  document_rejection: boolean;
  missing_document_reminder_days: number;
  system_maintenance: boolean;
  upcoming_deadline_alerts: boolean;
  final_decision_issued: boolean;
  created_at: string;
  updated_at: string;
}

@Injectable()
export class NotificationSettingsStorageService {
  private readonly logger = new Logger(NotificationSettingsStorageService.name);
  private readonly configDir = path.join(
    process.cwd(),
    'config',
    'notification-settings',
  );
  private readonly lockMap = new Map<string, Promise<any>>();

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * Ensure directory exists
   */
  private async ensureDirectory(): Promise<void> {
    try {
      await fs.access(this.configDir);
    } catch {
      await fs.mkdir(this.configDir, { recursive: true });
      this.logger.log(`Created directory: ${this.configDir}`);
    }
  }

  /**
   * Get file path for a user's notification settings
   */
  private getFilePath(userId: string): string {
    return path.join(this.configDir, `${userId}.json`);
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get current ISO date string
   */
  private getCurrentISOString(): string {
    return new Date().toISOString();
  }

  /**
   * Atomic file write with locking
   */
  private async atomicWrite(
    filePath: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    const lockKey = filePath;

    // If there's already a write operation for this file, wait for it
    if (this.lockMap.has(lockKey)) {
      await this.lockMap.get(lockKey);
    }

    // Create a new write operation
    const writeOperation = this.performAtomicWrite(filePath, data);
    this.lockMap.set(lockKey, writeOperation);

    try {
      await writeOperation;
    } finally {
      this.lockMap.delete(lockKey);
    }
  }

  /**
   * Perform the actual atomic write operation
   */
  private async performAtomicWrite(
    filePath: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    const tempFilePath = `${filePath}.tmp`;
    const backupFilePath = `${filePath}.backup`;
    let needsBackup = false;

    try {
      // Ensure directory exists
      await this.ensureDirectory();

      // Create backup if original file exists
      try {
        await fs.access(filePath);
        needsBackup = true;
        await fs.copyFile(filePath, backupFilePath);
      } catch {
        // Original file doesn't exist, no backup needed
        needsBackup = false;
      }

      // Write to temporary file
      await fs.writeFile(tempFilePath, JSON.stringify(data, null, 2), 'utf8');

      // Atomic move from temp to final location
      await fs.rename(tempFilePath, filePath);

      // Remove backup on successful write (only if backup was created)
      if (needsBackup) {
        try {
          await fs.unlink(backupFilePath);
        } catch {
          // Backup file cleanup failed, but write was successful
          this.logger.warn(`Failed to clean up backup file: ${backupFilePath}`);
        }
      }

      this.logger.log(`Successfully wrote notification settings: ${filePath}`);
    } catch (error) {
      // Clean up temp file
      try {
        await fs.unlink(tempFilePath);
      } catch {
        // Temp file might not exist
      }

      // Restore from backup if it exists
      if (needsBackup) {
        try {
          await fs.access(backupFilePath);
          await fs.copyFile(backupFilePath, filePath);
          await fs.unlink(backupFilePath);
          this.logger.warn(`Restored from backup: ${filePath}`);
        } catch {
          // No backup to restore from or restore failed
        }
      }

      this.logger.error(
        `Failed to write notification settings ${filePath}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Read notification settings from file
   */
  async readSettings(userId: string): Promise<NotificationSettingsData | null> {
    const filePath = this.getFilePath(userId);

    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data) as NotificationSettingsData;
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null; // File doesn't exist
      }

      this.logger.error(
        `Failed to read notification settings ${filePath}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Write notification settings to file
   */
  async writeSettings(
    userId: string,
    data: NotificationSettingsData,
  ): Promise<void> {
    const filePath = this.getFilePath(userId);
    await this.atomicWrite(filePath, data);
  }

  /**
   * Update notification settings (merge with existing)
   * This ensures PUT operations preserve existing fields
   */
  async updateSettings(
    userId: string,
    updates: Partial<NotificationSettingsData>,
  ): Promise<NotificationSettingsData> {
    const filePath = this.getFilePath(userId);
    const lockKey = filePath;

    // Ensure atomic read-modify-write
    if (this.lockMap.has(lockKey)) {
      await this.lockMap.get(lockKey);
    }

    const updateOperation = this.performUpdate(userId, updates);
    this.lockMap.set(lockKey, updateOperation);

    try {
      return await updateOperation;
    } finally {
      this.lockMap.delete(lockKey);
    }
  }

  /**
   * Perform the actual update operation
   */
  private async performUpdate(
    userId: string,
    updates: Partial<NotificationSettingsData>,
  ): Promise<NotificationSettingsData> {
    const existingData = await this.readSettings(userId);

    // If no existing data, create default settings merged with updates in a single write
    if (!existingData) {
      const defaultSettings = this.createDefaultSettings(userId);
      const mergedData = {
        ...defaultSettings,
        ...updates,
        updated_at: this.getCurrentISOString(),
      } as NotificationSettingsData;

      await this.writeSettings(userId, mergedData);
      return mergedData;
    }

    // Merge updates with existing data, preserving all existing fields
    const mergedData = {
      ...existingData,
      ...updates,
      updated_at: this.getCurrentISOString(),
    } as NotificationSettingsData;

    await this.writeSettings(userId, mergedData);
    return mergedData;
  }

  /**
   * Create default notification settings
   */
  createDefaultSettings(userId: string): NotificationSettingsData {
    const currentTime = this.getCurrentISOString();
    return {
      id: this.generateId(),
      user_id: userId,
      agent_assigned: true,
      case_status_update: true,
      agent_query: true,
      document_rejection: true,
      missing_document_reminder_days: 7,
      system_maintenance: true,
      upcoming_deadline_alerts: true,
      final_decision_issued: true,
      created_at: currentTime,
      updated_at: currentTime,
    };
  }

  /**
   * Delete notification settings file
   */
  async deleteSettings(userId: string): Promise<void> {
    const filePath = this.getFilePath(userId);

    try {
      await fs.unlink(filePath);
      this.logger.log(`Deleted notification settings: ${filePath}`);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return; // File doesn't exist, nothing to delete
      }

      this.logger.error(
        `Failed to delete notification settings ${filePath}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if settings file exists
   */
  async settingsExist(userId: string): Promise<boolean> {
    const filePath = this.getFilePath(userId);

    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
